<!DOCTYPE html>
<html>
<head>
    <title>Hint Functionality Test</title>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    
    <style>
      /* Timer freeze animation */
      .timer-frozen {
        animation: pulse-freeze 1.5s infinite;
        border: 2px solid #ffc107;
        border-radius: 8px;
        padding: 5px;
        background-color: #fff3cd;
      }
      
      @keyframes pulse-freeze {
        0% { opacity: 1; }
        50% { opacity: 0.6; }
        100% { opacity: 1; }
      }
      
      .timer-unfreezing {
        animation: unfreeze-flash 0.8s ease-out;
      }
      
      @keyframes unfreeze-flash {
        0% { background-color: #d4edda; transform: scale(1.05); }
        50% { background-color: #c3e6cb; transform: scale(1.1); }
        100% { background-color: transparent; transform: scale(1); }
      }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h2>Hint Functionality Test</h2>
        
        <div class="row">
            <div class="col-md-6">
                <h4>Timer Test</h4>
                <div id="timer-display" class="h3 text-primary">5:00</div>
                <button id="freeze-timer" class="btn btn-warning">Freeze Timer</button>
                <button id="unfreeze-timer" class="btn btn-success">Unfreeze Timer</button>
            </div>
            
            <div class="col-md-6">
                <h4>Hint Display Test</h4>
                <div id="hint-section" style="display: none; border: 3px solid #ffc107; background-color: #fff3cd; padding: 20px; border-radius: 8px;">
                    <h5 class="text-warning">💡 Hint: Optimal Moves</h5>
                    <div class="text-center">
                        <div>The best moves were:</div>
                        <div id="hint-moves" class="h5 text-dark font-weight-bold"></div>
                    </div>
                    <button id="continue-after-hint" class="btn btn-warning">✅ Continue Training</button>
                </div>
                <button id="show-hint" class="btn btn-info">Show Hint</button>
                <button id="hide-hint" class="btn btn-secondary">Hide Hint</button>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h4>UCI to SAN Conversion Test</h4>
                <input type="text" id="uci-input" class="form-control" placeholder="Enter UCI moves (e.g., e2e4 e7e5)" value="e2e4 e7e5">
                <button id="convert-uci" class="btn btn-primary mt-2">Convert to SAN</button>
                <div id="san-output" class="mt-2"></div>
            </div>
        </div>
    </div>

    <script>
        // Timer functions
        function freezeTimer() {
            $('#timer-display').addClass('timer-frozen');
        }

        function unfreezeTimer() {
            $('#timer-display').removeClass('timer-frozen').addClass('timer-unfreezing');
            
            setTimeout(function() {
                $('#timer-display').removeClass('timer-unfreezing');
            }, 800);
        }

        // Hint functions
        function showHint(moves) {
            $('#hint-moves').text(moves);
            $('#hint-section').show();
        }

        function hideHint() {
            $('#hint-section').hide();
        }

        // Simple UCI to SAN conversion (mock)
        function convertUciToSan(uciMovesString) {
            if (!uciMovesString || uciMovesString.trim() === '') {
                return 'No moves available';
            }
            
            var uciMoves = uciMovesString.trim().split(' ').filter(function(move) {
                return move.trim() !== '';
            });
            
            if (uciMoves.length === 0) {
                return 'No moves available';
            }
            
            // Simple conversion mapping for common moves (for testing)
            var uciToSan = {
                'e2e4': 'e4',
                'e7e5': 'e5',
                'g1f3': 'Nf3',
                'b8c6': 'Nc6',
                'f1c4': 'Bc4',
                'd7d6': 'd6',
                'f6f5': 'f5'
            };
            
            var sanMoves = [];
            for (var i = 0; i < uciMoves.length; i++) {
                var san = uciToSan[uciMoves[i]] || uciMoves[i];
                sanMoves.push(san);
            }
            
            return sanMoves.join(', ');
        }

        // Event handlers
        $('#freeze-timer').click(function() {
            freezeTimer();
        });

        $('#unfreeze-timer').click(function() {
            unfreezeTimer();
        });

        $('#show-hint').click(function() {
            freezeTimer();
            showHint();
        });

        $('#hide-hint').click(function() {
            hideHint();
            unfreezeTimer();
        });

        $('#continue-after-hint').click(function() {
            hideHint();
            unfreezeTimer();
        });

        $('#convert-uci').click(function() {
            var uciInput = $('#uci-input').val();
            var sanOutput = convertUciToSan(uciInput);
            $('#san-output').html('<strong>SAN:</strong> ' + sanOutput);
        });

        // Test the conversion on page load
        $(document).ready(function() {
            $('#convert-uci').click();
        });
    </script>
</body>
</html>
