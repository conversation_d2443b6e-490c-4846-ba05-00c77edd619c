#!/usr/bin/env python3
"""
Test script to verify the training summary functionality.
This script tests the HTML/JavaScript implementation by checking the structure.
"""

import os
import re

def test_summary_modal_structure():
    """Test that the training summary modal is properly structured in the HTML."""
    
    html_path = "src/templates/bbc.html"
    
    if not os.path.exists(html_path):
        print("❌ HTML file not found")
        return False
    
    with open(html_path, 'r') as f:
        content = f.read()
    
    # Check for training summary modal
    if 'id="training-summary-modal"' not in content:
        print("❌ Training summary modal not found")
        return False
    
    # Check for required elements
    required_elements = [
        'id="summary-moves-passed"',
        'id="summary-end-reason"',
        'id="summary-duration"',
        'id="summary-engine-used"',
        'id="summary-orientation-used"',
        'id="repeat-training"',
        'id="close-summary"'
    ]
    
    for element in required_elements:
        if element not in content:
            print(f"❌ Required element {element} not found")
            return False
    
    print("✅ Training summary modal structure is correct")
    
    # Check for showTrainingSummary function
    if 'function showTrainingSummary' not in content:
        print("❌ showTrainingSummary function not found")
        return False
    
    print("✅ showTrainingSummary function found")
    
    # Check for updated stopTraining function signature
    if 'function stopTraining(message, endReason)' not in content:
        print("❌ Updated stopTraining function signature not found")
        return False
    
    print("✅ Updated stopTraining function signature found")
    
    # Check for training state variables
    required_vars = [
        'var trainingStartTime = null',
        'var trainingEndReason = null'
    ]
    
    for var in required_vars:
        if var not in content:
            print(f"❌ Required variable {var} not found")
            return False
    
    print("✅ Required training state variables found")
    
    # Check for event handlers
    event_handlers = [
        "$('#repeat-training').on('click'",
        "$('#close-summary').on('click'"
    ]
    
    for handler in event_handlers:
        if handler not in content:
            print(f"❌ Event handler {handler} not found")
            return False
    
    print("✅ Event handlers found")
    
    # Check for emoji usage in end reasons
    emojis = ['⏰', '🏳️', '❌', '⏹️', '⚠️']
    emoji_found = any(emoji in content for emoji in emojis)
    
    if not emoji_found:
        print("❌ End reason emojis not found")
        return False
    
    print("✅ End reason emojis found")
    
    return True

def test_stoptraining_calls():
    """Test that all stopTraining calls have been updated with end reasons."""
    
    html_path = "src/templates/bbc.html"
    
    with open(html_path, 'r') as f:
        content = f.read()
    
    # Find all stopTraining calls
    pattern = r"stopTraining\([^)]*\)"
    calls = re.findall(pattern, content)
    
    print(f"Found {len(calls)} stopTraining calls:")
    
    expected_calls = [
        "stopTraining('Error occurred during training.', 'error')",
        "stopTraining('Time is up!', 'timeout')",
        "stopTraining('Training session ended by surrender.', 'surrender')",
        "stopTraining('', 'manual')",
        "stopTraining('Wrong move detected.', 'wrong_move')"
    ]
    
    for call in calls:
        print(f"  - {call}")
    
    # Check that we have calls with different end reasons
    end_reasons = ['timeout', 'surrender', 'wrong_move', 'manual', 'error']
    
    for reason in end_reasons:
        if not any(reason in call for call in calls):
            print(f"❌ Missing stopTraining call with end reason: {reason}")
            return False
    
    print("✅ All required stopTraining calls with end reasons found")
    return True

def main():
    """Run all tests."""
    print("🧪 Testing Training Summary Functionality")
    print("=" * 50)
    
    tests = [
        test_summary_modal_structure,
        test_stoptraining_calls
    ]
    
    all_passed = True
    
    for test in tests:
        print(f"\n🔍 Running {test.__name__}...")
        if not test():
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All tests passed! Training summary functionality is implemented correctly.")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    return all_passed

if __name__ == "__main__":
    main()
