#!/usr/bin/env python3
"""
Test script to verify the wrong move penalty system.
This script checks that the implementation correctly handles wrong moves with time penalties.
"""

import os
import re

def test_penalty_system_implementation():
    """Test that the penalty system is properly implemented."""
    
    html_path = "src/templates/bbc.html"
    
    if not os.path.exists(html_path):
        print("❌ HTML file not found")
        return False
    
    with open(html_path, 'r') as f:
        content = f.read()
    
    # Check that wrong move penalty is implemented in continue-after-hint handler
    if 'remainingTime = Math.max(0, remainingTime - 30)' not in content:
        print("❌ 30-second penalty not found in continue-after-hint handler")
        return False
    
    print("✅ 30-second penalty found in continue-after-hint handler")
    
    # Check that penalty is also applied when no hints are available
    penalty_pattern = r'remainingTime = Math\.max\(0, remainingTime - 30\)'
    penalty_matches = re.findall(penalty_pattern, content)
    
    if len(penalty_matches) < 2:
        print("❌ Penalty should be applied in both hint and no-hint scenarios")
        return False
    
    print("✅ Penalty applied in both hint and no-hint scenarios")
    
    # Check that training restarts after penalty (unless time runs out)
    if 'restartTrainingRound()' not in content:
        print("❌ Training restart not found")
        return False
    
    print("✅ Training restart functionality found")
    
    # Check that timeout is triggered if penalty causes time to run out
    timeout_check_pattern = r'if \(remainingTime <= 0\) \{\s*stopTraining\([^)]*timeout[^)]*\)'
    if not re.search(timeout_check_pattern, content, re.MULTILINE):
        print("❌ Timeout check after penalty not found")
        return False
    
    print("✅ Timeout check after penalty found")
    
    # Check that wrong_move is no longer an end reason in summary
    if "'wrong_move'" in content and "endReasonDisplay = '❌ Wrong Move'" in content:
        print("❌ wrong_move end reason still present in summary")
        return False
    
    print("✅ wrong_move end reason removed from summary")
    
    # Check that penalty warning is shown in hint modal
    if "30 seconds will be deducted" not in content:
        print("❌ Penalty warning not found in hint modal")
        return False
    
    print("✅ Penalty warning found in hint modal")
    
    # Check that continue button shows penalty indication
    if "Continue Training (-30s)" not in content:
        print("❌ Penalty indication not found in continue button")
        return False
    
    print("✅ Penalty indication found in continue button")
    
    return True

def test_no_wrong_move_stoptraining_calls():
    """Test that stopTraining is not called with 'wrong_move' reason anymore."""
    
    html_path = "src/templates/bbc.html"
    
    with open(html_path, 'r') as f:
        content = f.read()
    
    # Check that there are no stopTraining calls with 'wrong_move'
    if "stopTraining(" in content and "'wrong_move'" in content:
        # Find all stopTraining calls
        stoptraining_pattern = r"stopTraining\([^)]*'wrong_move'[^)]*\)"
        wrong_move_calls = re.findall(stoptraining_pattern, content)
        
        if wrong_move_calls:
            print(f"❌ Found stopTraining calls with 'wrong_move': {wrong_move_calls}")
            return False
    
    print("✅ No stopTraining calls with 'wrong_move' found")
    return True

def test_end_reasons_updated():
    """Test that only valid end reasons remain."""
    
    html_path = "src/templates/bbc.html"
    
    with open(html_path, 'r') as f:
        content = f.read()
    
    # Valid end reasons after removing wrong_move
    valid_reasons = ['timeout', 'surrender', 'manual', 'error']
    
    # Check that all valid reasons are present
    for reason in valid_reasons:
        if f"case '{reason}':" not in content:
            print(f"❌ Valid end reason '{reason}' not found")
            return False
    
    print("✅ All valid end reasons found")
    
    # Check that wrong_move case is removed
    if "case 'wrong_move':" in content:
        print("❌ wrong_move case still present in switch statement")
        return False
    
    print("✅ wrong_move case removed from switch statement")
    
    return True

def main():
    """Run all tests."""
    print("🧪 Testing Wrong Move Penalty System")
    print("=" * 50)
    
    tests = [
        test_penalty_system_implementation,
        test_no_wrong_move_stoptraining_calls,
        test_end_reasons_updated
    ]
    
    all_passed = True
    
    for test in tests:
        print(f"\n🔍 Running {test.__name__}...")
        if not test():
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All tests passed! Wrong move penalty system is implemented correctly.")
        print("\n📋 Summary of penalty system:")
        print("  • Wrong moves trigger 30-second time penalty")
        print("  • Training continues after penalty (unless time runs out)")
        print("  • Hint modal shows penalty warning")
        print("  • Continue button indicates penalty (-30s)")
        print("  • Training only stops for: timeout, surrender, manual stop, error")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    return all_passed

if __name__ == "__main__":
    main()
